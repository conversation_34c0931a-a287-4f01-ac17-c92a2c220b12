package com.yxt.talent.rv.infrastructure.repository.user;

import com.yxt.CommonRepository;
import com.yxt.talent.rv.domain.user.entity.UserPrjSummary;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.UserPrjSummaryMapper;
import com.yxt.talent.rv.infrastructure.repository.user.assembler.UserPrjSummaryAssembler;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Optional;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;

@Slf4j
@Repository
@RequiredArgsConstructor
class UserPrjSummaryRepo implements CommonRepository {

    private final UserPrjSummaryAssembler userPrjSummaryAssembler;
    private final UserPrjSummaryMapper userPrjSummaryMapper;

    public Optional<Collection<UserPrjSummary>> loadByUserId(String orgId, String entityId) {
        return Optional.of(userPrjSummaryMapper.selectByUserId(orgId, entityId))
                .map(userPrjSummaryAssembler::toUserPrjSummaries);
    }

    void save(@NonNull UserPrjSummary entity) {
        String orgId = entity.getOrgId();
        deleteConvertUpdate(orgId, entity, userPrjSummaryAssembler::toUserPrjSummaryPo,
                userPrjSummaryMapper::deleteByOrgIdAndId, userPrjSummaryMapper::insertOrUpdate);
    }

    void save(@NonNull Collection<UserPrjSummary> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13465:");
            return;
        }
        entities.forEach(this::save);
    }

    void delete(@NonNull UserPrjSummary entity) {
        EntityUtil.delete(entity).ifPresent(this::save);
    }

    void delete(@NonNull Collection<UserPrjSummary> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13455:");
            return;
        }
        entities.forEach(this::delete);
    }
}

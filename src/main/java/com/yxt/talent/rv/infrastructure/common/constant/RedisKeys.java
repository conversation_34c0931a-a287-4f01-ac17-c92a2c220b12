package com.yxt.talent.rv.infrastructure.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 锁表头：LK - lock key
 * 缓存标识:CK - cache Key
 */
@UtilityClass
public final class RedisKeys {

    /* cache key*/
    public static final String CK_ORG = "sprv:ck:org";
    public static final String CK_ORG_ROOT_DEPT = "sprv:ck:org:dept:root";
    public static final String CK_ORG_INIT_DEMO = "sprv:ck:org:init:%s";
    public static final String CK_ORG_LOCALIZATION = "sprv:ck:org:localization:%s";

    public static final String CK_ORG_PERF_GRADE = "sprv:ck:org:perf:grade";

    /**
     * 用户信息缓存
     */
    public static final String CK_USER_INFO_CACHE = "sprv:ck:user:info:%s";

    public static final String CK_USER_AUTH_CLIENT = "sprv:ck:user:auth:client:%s:%s";
    public static final String CK_USER_AUTH_CLIENT_TEAM = "sprv:ck:user:auth:client:team:%s:%s:%s";
    public static final String CK_USER_AUTH_MGR_NAV_CODE = "sprv:ck:user:auth:mgr:%s:%s";

    public static final String CK_PRJ_PROGRESS = "sprv:ck:prj:progress:%s";

    public static final String CK_CALI_MEET_CHANGE_STATUS = "sprv:ck:calimeet:status:change";

    public static final String CK_DMP_USER_TAG = "sprv:ck:dmp:user:tag:%s:%s";
    public static final String CK_DMP_FORM_EVAL_RELATION = "sprv:ck:dmp:form:eval:relation:%s:%s";


    /* lock Key */
    public static final String LK_ORG_DIM_INIT = "sprv:lk:org:dim:init:%s";
    public static final String LK_ORG_PROFILE_RV_DETAIL_EXPT = "sprv:lk:org:profile:rv:detail:%s:%s";
    public static final String LK_ORG_SETTING_INIT = "sprv:lk:org:setting:init:%s";
    public static final String LK_ORG_PERF_GRADE_INIT = "sprv:lk:org:perf:grade:init:%s";
    public static final String LK_ORG_PERF_GRADE_OP = "sprv:lk:org:perf:grade:op:%s";

    public static final String LK_CALI_MEET_USER_RESULT_EXPT = "sprv:lk:calimeet:user:result:expt:%s:%s";
    public static final String LK_CALI_MEET_USER_TMPL_EXPT = "sprv:lk:calimeet:user:tmpl:expt:%s:%s";
    public static final String LK_CALI_MEET_USER_RESULT_TYPE_IMPT = "sprv:lk:calimeet:user:result:impt:%s:%s:%s";

    /**
     * 导入校准人员
     */
    public static final String LK_CALI_MEET_USER_IMPT = "sprv:lk:calimeet:user:impt:%s:%s";

    public static final String LK_PRJ_DIM_TYPE_EXPT = "sprv:lk:prj:dim:expt:%s:%s:%s:%s";
    public static final String LK_PRJ_DIM_TYPE_IMPT = "sprv:lk:prj:dim:impt:%s:%s:%s:%s";
    public static final String LK_PRJ_RESULT_EXPT = "sprv:lk:prj:result:expt:%s:%s";
    public static final String LK_PRJ_OVERVIEW_DEPT_GROUP_EXPT = "sprv:lk:prj:overview:dept:group:expt:%s:%s";
    public static final String LK_PRJ_LABEL_INIT = "sprv:lk:prj:label:init:%s";
    public static final String LK_PRJ_USER_IMPT = "sprv:lk:prj:user:impt:%s:%s";
    public static final String LK_PRJ_RESULT_CONFIG = "sprv:lk:prj:config:%s";
    public static final String LK_PRJ_RESULT_CONFIG_INIT = "sprv:lk:prj:config:init:%s";

    public static final String LK_PERF_EXPT = "sprv:lk:perf:%s:%s";
    public static final String LK_PERF_IMPT = "sprv:lk:perf:impt:%s:%s:%s";

    public static final String LK_CALI_DIM_LEVEL_IMPT = "sprv:lk:cali:dim:level:impt:%s:%s:%s";
    public static final String LK_CALI_DIM_IMPT = "sprv:lk:cali:dim:impt:%s:%s:%s";

    public static final String LK_DMP_TASK_USER_EXPORT = "sprv:lk:dmp:task:user:expt:%s:%s";
    public static final String LK_DMP_USER_IMPORT = "sprv:lk:dmp:user:impt:%s:%s";
    public static final String LK_DMP_USER_RESULT_CALCULATOR = "sprv:lk:dmp:user:result:%s:%s";
    public static final String LK_DMP_USER_RESULT_EXPORT = "sprv:lk:dmp:user:result:expt:%s:%s";
    public static final String LK_DMP_USER_RESULT_COORDINATOR = "sprv:lk:dmp:user:result:coordinator:%s:%s";

    public static final String CACHE_COPY_ORG_IDMAP = "sprv:copy:org:idmap:%s";


    public static final String CACHE_ACT_PROFILE_CAL = "sprv:act:profile:cal:%s:%s";

    public static final String CACHE_ACT_PROFILE_CAL_INTERFACE = "sprv:act:profile:interface:cal:%s:%s";
    public static final String LK_ACT_PERF_TEMPLATE_EXPT = "sprv:lk:act:perf:template:%s:%s";
    public static final String LK_ACT_PERF_IMPT = "sprv:lk:act:perf:impt:%s:%s:%s";

    public static final String LK_XPD_RULE_GEN = "sprv:lk:xpd:rule:gen:%s:%s";
    public static final String CACHE_ACT_PERF_CAL = "sprv:act:perf:cal:%s:%s";
    // 活动导入-指标导出
    public static final String LK_XPD_IMPORT_IND_TEMP_EXPORT = "sprv:lk:xpd:import:ind:temp:export:%s:%s";
    public static final String LK_XPD_IMPORT_DIM_TEMP_EXPORT = "sprv:lk:xpd:import:dim:temp:export:%s:%s";
    public static final String LK_XPD_IMPORT_ERROR_EXPORT = "sprv:lk:xpd:import:error:export:%s:%s";

    public static final String LK_XPD_ACT_IMPORT = "sprv:lk:xpd:import:act:%s:%s";
    public static final String ORG_INFO_CACHE_KEY = "sprv:org:info:%s:%s";

    /**
     * 新盘点结果计算锁 param xpdId
     */
    public static final String LK_XPD_CALC_EXECUTE = "sprv:lk:xpd:calc:execute:%s";

    public static final String CACHE_SEND_MESSAGE = "sprv:cache:send:message:%s:%s";

    // 活动计算状态  0-进行中 1-完成
    public static final String CK_ACTV_CALC = "sprv:ck:actv:calc:%s:%s";

    //盘点迁移指标结果计算，参数：xpdId
    public static final String CACHE_CALC_MIG_INDICATOR_RESULT = "sprv:calc:mig:indicator:result:%s";

    public static final String CACHE_TALENT_USER_EXT_IMPORT = "sprv:talent:user:ext:import:%s:%s";

    public static final String LK_CALI_IMPT = "sprv:lk:cali:impt:%s:%s";
}

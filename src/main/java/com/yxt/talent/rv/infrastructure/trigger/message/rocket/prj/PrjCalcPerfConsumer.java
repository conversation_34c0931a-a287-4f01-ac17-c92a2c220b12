package com.yxt.talent.rv.infrastructure.trigger.message.rocket.prj;

import com.yxt.common.util.BeanHelper;
import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.prj.event.PrjCalcPerfMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PSI_C_PRJ_CALC_PERF;

/**
 * 项目盘点-绩效维度-计算
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_PSI_C_PRJ_CALC_PERF,         topic = TOPIC_PSI_C_PRJ_CALC_PERF,         consumeThreadNumber = 2, consumeTimeout = 30)
public class PrjCalcPerfConsumer implements RocketMQListener<PrjCalcPerfMessageEvent> {

    private final EventPublisher eventPublisher;

    @Trace
    @Override
    public void onMessage(PrjCalcPerfMessageEvent message) {
        try {
            log.debug("LOG12935:{}", BeanHelper.bean2Json(message, ALWAYS));
            eventPublisher.publish(message);
        } catch (Throwable e) {
            log.error("LOG65300:", e);
        }
    }


}

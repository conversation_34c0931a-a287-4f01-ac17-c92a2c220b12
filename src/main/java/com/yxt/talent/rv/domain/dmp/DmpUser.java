package com.yxt.talent.rv.domain.dmp;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.AuditAggregateRoot;
import com.yxt.EntityLoadConfig;
import com.yxt.talent.rv.domain.dmp.entity.user.DmpUserDimDetail;
import com.yxt.talent.rv.domain.dmp.entity.user.DmpUserDimResult;
import com.yxt.talent.rv.domain.dmp.entity.user.DmpUserResult;
import com.yxt.talent.rv.domain.dmp.entity.user.DmpUserTaskResult;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.util.NonRemovableLinkedHashSet;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Set;

/**
 * 人岗动态匹配项目-学员
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false, of = {"orgId", "dmpId", "userId"})
public class DmpUser extends AuditAggregateRoot<String, LocalDateTime> {

    /**
     * 机构id
     */
    @Nonnull
    private String orgId;

    /**
     * 人岗动态匹配项目id
     */
    @Nonnull
    private String dmpId;

    /**
     * 人员id
     */
    @Nonnull
    private String userId;

    /**
     * 加入时间
     */
    private LocalDateTime joinTime;

    /**
     * 加入方式（0-手动加入 1-自动加入）
     */
    private Integer joinType;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient JoinType joinTypeEnum;

    public void setJoinType(Integer joinType) {
        this.joinType = joinType;
        this.joinTypeEnum = JoinType.byCode(joinType);
    }

    /**
     * 用户的匹配胜任结果
     */
    @Nullable
    private DmpUserResult dmpUserResult;

    /**
     * 用户的维度匹配明细，每个条件的匹配结果
     */
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<DmpUserDimDetail> dmpUserDimDetails = new NonRemovableLinkedHashSet<>();

    /**
     * 用户的维度匹配结果
     */
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<DmpUserDimResult> dmpUserDimResults = new NonRemovableLinkedHashSet<>();

    /**
     * 用户任务的完成结果
     **/
    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<DmpUserTaskResult> dmpUserTaskResults = new NonRemovableLinkedHashSet<>();


    public void addDmpUserDimDetail(DmpUserDimDetail entity) {
        if (entity == null) {
            log.warn("LOG10115:dmpId={}, entityId={}", this.dmpId, this.getId());
            return;
        }
        dmpUserDimDetails.add(entity);
    }

    public void addDmpUserDimDetails(Collection<DmpUserDimDetail> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG11265:dmpId={}, entityId={}", this.dmpId, this.getId());
            return;
        }
        dmpUserDimDetails.addAll(entities);
    }

    public void addDmpUserDimResult(DmpUserDimResult entity) {
        if (entity == null) {
            log.warn("LOG10095:dmpId={}, entityId={}", this.dmpId, this.getId());
            return;
        }
        dmpUserDimResults.add(entity);
    }

    public void addDmpUserDimResults(Collection<DmpUserDimResult> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG11135:dmpId={}, entityId={}", this.dmpId, this.getId());
            return;
        }
        dmpUserDimResults.addAll(entities);
    }

    public void addDmpUserTaskResult(DmpUserTaskResult entity) {
        if (entity == null) {
            log.warn("LOG10055:dmpId={}, entityId={}", this.dmpId, this.getId());
            return;
        }
        dmpUserTaskResults.add(entity);
    }

    public void addDmpUserTaskResults(Collection<DmpUserTaskResult> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG10045:dmpId={}, entityId={}", this.dmpId, this.getId());
            return;
        }
        dmpUserTaskResults.addAll(entities);
    }

    /**
     * 标记删除dmpUser及其关联的所有实体
     */
    public void delete() {
        EntityUtil.delete(this);
        EntityUtil.delete(this.dmpUserResult);
        EntityUtil.delete(this.dmpUserDimDetails);
        EntityUtil.delete(this.dmpUserDimResults);
        EntityUtil.delete(this.dmpUserTaskResults);
    }

    @Getter
    @RequiredArgsConstructor
    public enum JoinType {
        /**
         * 加入方式
         */
        UNKNOWN(-1, "未知"),
        MANUAL(0, "手动加入"),
        AUTO(1, "自动加入");

        private final Integer code;
        private final String desc;

        public static JoinType byCode(@Nullable Integer joinType) {
            for (JoinType value : values()) {
                if (value.code.equals(joinType)) {
                    return value;
                }
            }
            return UNKNOWN;
        }
    }

    @Builder
    public record LoadConfig(
            boolean loadDmpUserDimDetails,
            boolean loadDmpUserDimResults,
            boolean loadDmpUserTaskResults
    ) implements EntityLoadConfig {
        public static final LoadConfig WITH_ALL = new LoadConfig(true, true, true);
        public static final LoadConfig ONLY_SELF = new LoadConfig(false, false, false);
    }
}

package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2024/12/19
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DimResultImportDTO implements ImportContent {

    private String fullName;

    private String userName;

    private String dimLevel;

    private String userId;

    private String gridLevelId;

    private String errorMsg;

}

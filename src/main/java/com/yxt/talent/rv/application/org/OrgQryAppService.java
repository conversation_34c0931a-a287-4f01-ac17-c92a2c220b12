package com.yxt.talent.rv.application.org;

import com.yxt.ApplicationQueryService;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.coreapi.client.bean.sale.OrgVerifyFactorBean;
import com.yxt.talent.rv.controller.manage.org.viewobj.OrgSettingParamVO;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.service.auth.FactorService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.org.OrgBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@ApplicationQueryService
public class OrgQryAppService {

    private final FactorService factorService;
    private final UdpAclService udpAclService;
    private final AppProperties appProperties;

    /**
     * 获取机构参
     *
     * @param operator
     */
    public OrgSettingParamVO getOrgParam(UserCacheDetail operator) {
        return setOrgEditionForV2(operator.getOrgId());
    }

    /**
     * 获取2.0版本机构参
     *
     * @param orgId
     */
    private OrgSettingParamVO setOrgEditionForV2(String orgId) {
        OrgSettingParamVO orgSettingParamGet = new OrgSettingParamVO();
        // 机构开通的 0-未开通，1-基础版，2-测训版，3-高级版
        int orgEdition = factorService.getOrgEdition(orgId);
        orgSettingParamGet.setOnlyTalentTraining(0L);
        OrgVerifyFactorBean rvParamValue =
            factorService.getVerifyFactorBean(orgId, FactorService.FACTOR_CODE_SP_TALENT_PLUS);
        orgSettingParamGet.setTalentRvStatus(
            null == rvParamValue ? 0L : (long) rvParamValue.getFactorState());
        orgSettingParamGet.setOrgEdition(orgEdition);
        return orgSettingParamGet;
    }

    /**
     * 检测当前查询机构是否是演示demo机构，如果是，则返回demo模版机构id
     *
     * @param orgId
     */
    public String demoCopyOrgId(String orgId) {
        OrgBean orgBean = udpAclService.getOrgInfo(orgId);
        if (orgBean != null && Objects.equals(orgBean.getOrgType(), 2) &&
            StringUtils.isNotBlank(orgBean.getSalesOrgTemplateId()) &&
            StringUtils.isNotBlank(appProperties.getDemoOrgId())) {
                return appProperties.getDemoOrgId().trim();
        }
        return orgId;
    }
}

package com.yxt.talent.rv.application.activity.slot;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.activity.facade.bean.control.ResultCopyReq;
import com.yxt.aom.base.custom.SingleActivityCompo;
import com.yxt.aom.base.entity.control.ActivityObjectiveResult;
import com.yxt.aom.base.entity.control.AssessmentActivityResult;
import com.yxt.aom.base.entity.control.BaseActivityResult;
import com.yxt.spsdfacade.constants.SdDemoConstants;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.talent.rv.application.activity.dto.UserIndicatorPushDataDTO;
import com.yxt.talent.rv.application.democopy.DemoTableProvider;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Component("singleActivityCompo4ActvProf")
public class SingleActivityCompo4ActvProf implements SingleActivityCompo {

    private final DemoTableProvider demoTableProvider;


    @Override
    public void resultRollUpCallBack(BaseActivityResult result) {
        // do nothing
    }

    @Override
    public void activityRepeat() {
        // do nothing
    }

    /**
     * demo复制时由活动方更新老的活动结果数据中相关id字段
     * (比如AssessmentActivityResult的ext，ActivityObjectiveResult的ext+objectiveId+objectiveModeId等)
     *
     * @param bean                 the bean
     * @param oldAssessmentResults the old assessment results
     * @param oldObjectiveResults  the old objective results
     */
    @Override
    public void updateIdFields4DemoCopy(
        ResultCopyReq bean,
        List<AssessmentActivityResult> oldAssessmentResults,
        List<ActivityObjectiveResult> oldObjectiveResults) {
        OrgInit4Mq orgInit = new OrgInit4Mq();
        orgInit.setTargetOrgId(bean.getTgtOrgId());
        orgInit.setSourceOrgId(bean.getSrcOrgId());
        DemoCopyRunner runner = demoTableProvider.buildRunner(orgInit);
        if (CollectionUtils.isNotEmpty(oldObjectiveResults)) {
            for (ActivityObjectiveResult item : oldObjectiveResults) {
                if (StringUtils.isNotBlank(item.getObjectiveModeId())) {
                    item.setObjectiveModeId(transferNewId(runner, SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID,
                            item.getObjectiveModeId()));
                }
                if (StringUtils.isNotBlank(item.getObjectiveId())) {
                    item.setObjectiveId(
                            transferNewId(runner, SprvDemoOrgCopyConstants.SPSD_MODEL_ID, item.getObjectiveId()));
                }
            }
        }

    }

    private String transferNewId(DemoCopyRunner runner, String idMap, String sourceId) {
        if (StringUtils.isBlank(idMap) || StringUtils.isBlank(sourceId)) {
            return sourceId;
        }
        String newId = runner.getIdMapValue(idMap, sourceId);
        if (StringUtils.isBlank(newId)) {
            return sourceId;
        }
        return newId;
    }
}

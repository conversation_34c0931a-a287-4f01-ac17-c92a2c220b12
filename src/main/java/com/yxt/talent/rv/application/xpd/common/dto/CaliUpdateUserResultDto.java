package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CaliUpdateUserResultDto {
    //维度结果校准 sdDimId + gridLevelId/scoreValue/qualifiedPtg
    @Schema(description = "人才标准的维度id")
    private String sdDimId;
    @Schema(description = "宫格分层id, rv_xpd_grid_level.id")
    private String gridLevelId;
    @Schema(description = "分值, 包括绩效得分")
    private BigDecimal scoreValue;
    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;
    //指标结果校准,sdIndicatorId + scoreValue/qualified
    @Schema(description = "人才标准的维度id")
    private String sdIndicatorId;
    @Schema(description = "是否达标")
    private Integer qualified;
}

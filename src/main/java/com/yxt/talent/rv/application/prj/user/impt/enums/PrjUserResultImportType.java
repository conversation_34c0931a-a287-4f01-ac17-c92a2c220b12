package com.yxt.talent.rv.application.prj.user.impt.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PrjUserResultImportType {
    /* 导入盘点结果（维度等级） */
    LEVEL(1, "导入盘点结果（维度等级）"),

    /* 导入盘点结果（维度模型评分） */
    SCORE(2, "导入盘点结果（维度模型评分）");

    private final int code;
    private final String desc;

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public static boolean isValidType(int type) {
        for (PrjUserResultImportType value : PrjUserResultImportType.values()) {
            if (value.getCode() == type) {
                return true;
            }
        }
        return false;
    }
}

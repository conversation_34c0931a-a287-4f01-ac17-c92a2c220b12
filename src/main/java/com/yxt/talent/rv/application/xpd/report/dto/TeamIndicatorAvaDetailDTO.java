package com.yxt.talent.rv.application.xpd.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
public class TeamIndicatorAvaDetailDTO {
    @Schema(description = "能力id")
    private String indicatorId;

    @Schema(description = "能力名称")
    private String indicatorName;

    @Schema(description = "平均分")
    private BigDecimal averageScore;

    @Schema(description = "团队得分人数分布")
    private TeamIndicatorMapDTO teamIndicatorMap;
}

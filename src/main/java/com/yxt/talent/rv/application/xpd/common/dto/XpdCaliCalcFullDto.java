package com.yxt.talent.rv.application.xpd.common.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class XpdCaliCalcFullDto {
    private String orgId;
    private String aomPrjId;
    private int resultType;
    private Integer gridType;
    private int scoreSystem;
    //维度组合
    private List<DimCombBriefDto> combList;
    private Set<String> combUsedDimSet;
    //规则
    private List<XpdDimRule4Cali> ruleList;
    private Map<String, String> sdDimNameMap;
    private Map<String, XpdSdIndicatorScoreDto> sdIndicatorMap;
    private List<XpdGridLevelBriefDto> gridLevelList;
    private List<XpdRuleRefIndicatorDto> ruleRefIndicators;
    private Map<String, Set<String>> fastFormulaIndicatorMap;
    private List<XpdRuleLevelDto> xpdRuleLevels;
    private int xpdCaliCalcType;
    private String xpdCalcFormula;
    private XpdRule4CaliDto xpdRuleDto;
    private List<XpdRuleRefIndicatorsDto> allFormulaRefIndicators;
}

package com.yxt.talent.rv.controller.manage.xpd.log.viewobj;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class Xpd4CreateLogDTO {

    @AuditLogField(name = "项目名称", orderIndex = 1)
    private String actvName;
    @AuditLogField(name = "盘点分类", orderIndex = 2)
    private String categoryName;

    @AuditLogField(name = "场景名称", orderIndex = 3)
    private String sceneName;

    @AuditLogField(name = "盘点模型", orderIndex = 4)
    private String modelName;

    @AuditLogField(name = "盘点时间", orderIndex = 5)
    private String time;

    @AuditLogField(name = "盘点目标", orderIndex = 6)
    private String description;

    @AuditLogField(name = "结束设置", orderIndex = 7)
    private String autoEndDesc;

}

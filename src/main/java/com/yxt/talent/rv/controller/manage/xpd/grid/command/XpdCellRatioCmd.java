package com.yxt.talent.rv.controller.manage.xpd.grid.command;

import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdCellRatioVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class XpdCellRatioCmd {

    @Schema(description = "列表id")
    private String cellRatioId;

    /*@Schema(description = "宫格id集合")
    private List<String> cellIds;*/

    @Schema(description = "宫格id集合")
    private List<XpdCellRatioVO> cellRatioList;

    @Schema(description = "人员占比")
    private BigDecimal ratio;

}

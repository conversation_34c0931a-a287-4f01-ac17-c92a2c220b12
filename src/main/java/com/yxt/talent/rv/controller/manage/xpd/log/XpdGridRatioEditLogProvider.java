package com.yxt.talent.rv.controller.manage.xpd.log;

import com.alibaba.fastjson.JSON;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.grid.XpdGridAppManage;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.XpdCellRatioCmd;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.XpdTempRatioCmd;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdCellRatioVO;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridRatioDetailVO;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridRatioVO;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdGridRatioEditLogDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdDimCombMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridRatioMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridRatioPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/4/27
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class XpdGridRatioEditLogProvider implements AuditLogDataProvider<String, XpdGridRatioEditLogDto> {

    private final XpdGridRatioMapper gridRatioMapper;
    private final XpdGridMapper gridMapper;
    private final XpdDimCombMapper dimCombMapper;

    private final XpdGridAppManage gridAppManage;

    @Override
    public XpdGridRatioEditLogDto before(String param, AuditLogBasicBean logBasic) {
        /*if (CollectionUtils.isEmpty(param)) {
            return null;
        }
        XpdGridRatioEditLogDto dto = new XpdGridRatioEditLogDto();
        List<String> dimCombIds = param.stream().map(XpdTempRatioCmd::getDimCombId).toList();
        List<XpdDimCombPO> xpdDimCombs = dimCombMapper.selectByIds(dimCombIds);
        List<String> dimCombNames = xpdDimCombs.stream().map(XpdDimCombPO::getCombName).toList();
        dto.setDimComb(String.join(";", dimCombNames));

        XpdTempRatioCmd xpdTempRatioCmd = param.get(0);
        List<XpdCellRatioCmd> ratioDetailList = xpdTempRatioCmd.getRatioDetailList();
        if (CollectionUtils.isEmpty(ratioDetailList)) {
            return dto;
        }
        XpdCellRatioCmd xpdCellRatioCmd = ratioDetailList.get(0);
        String cellRatioId = null;
        for (XpdCellRatioCmd cellRatioCmd : ratioDetailList) {
            String cellRatioId1 = cellRatioCmd.getCellRatioId();
            if (StringUtils.isBlank(cellRatioId1)) {
                continue;
            }
            cellRatioId = cellRatioId1;
        }
        if (StringUtils.isBlank(cellRatioId)) {
            return null;
        }*/
        XpdGridRatioEditLogDto dto = new XpdGridRatioEditLogDto();
        /*XpdGridRatioPO xpdGridRatio = gridRatioMapper.selectByPrimaryKey(cellRatioId);
        String gridId = xpdGridRatio.getGridId();*/

        List<XpdGridRatioVO> gridRatioList = gridAppManage.getGridRatioList(param);
        Map<String, List<Map<String, String>>> resMap = new HashMap<>();
        for (XpdGridRatioVO xpdGridRatioVO : gridRatioList) {
            String dimCombName = xpdGridRatioVO.getDimCombName();
            List<Map<String, String>> mapList = new ArrayList<>();
            List<XpdGridRatioDetailVO> ratioDetailList1 = xpdGridRatioVO.getRatioDetailList();
            for (XpdGridRatioDetailVO xpdGridRatioDetailVO : ratioDetailList1) {
                Map<String, String> map = new HashMap<>();
                List<XpdCellRatioVO> cellRatioList = xpdGridRatioDetailVO.getCellRatioList();
                List<String> list = cellRatioList.stream().map(XpdCellRatioVO::getCellName).toList();
                map.put("落位宫格", String.join(",", list));
                map.put("人员占比", xpdGridRatioDetailVO.getRatio() + "%");
                mapList.add(map);
            }
            resMap.put(dimCombName, mapList);
        }

        dto.setPosRatio(JSON.toJSONString(resMap));

        return dto;
    }

    @Override
    public XpdGridRatioEditLogDto after(String param, AuditLogBasicBean logBasic) {
        /*XpdGridRatioEditLogDto dto = new XpdGridRatioEditLogDto();
        dto.setDimComb("--");
        if (CollectionUtils.isEmpty(param)) {
            return null;
        }

        XpdTempRatioCmd xpdTempRatioCmd = param.get(0);
        List<XpdCellRatioCmd> ratioDetailList = xpdTempRatioCmd.getRatioDetailList();
        if (CollectionUtils.isEmpty(ratioDetailList)) {
            return null;
        }*/

        /*XpdCellRatioCmd xpdCellRatioCmd = ratioDetailList.get(0);
        XpdGridRatioPO xpdGridRatio = gridRatioMapper.selectByPrimaryKey(xpdCellRatioCmd.getCellRatioId());
        XpdGridRatioEditLogDto dto = new XpdGridRatioEditLogDto();
        String gridId = xpdGridRatio.getGridId();*/

        XpdGridRatioEditLogDto dto = new XpdGridRatioEditLogDto();
        List<XpdGridRatioVO> gridRatioList = gridAppManage.getGridRatioList(param);
        Map<String, List<Map<String, String>>> resMap = new HashMap<>();
        for (XpdGridRatioVO xpdGridRatioVO : gridRatioList) {
            String dimCombName = xpdGridRatioVO.getDimCombName();
            List<Map<String, String>> mapList = new ArrayList<>();
            List<XpdGridRatioDetailVO> ratioDetailList1 = xpdGridRatioVO.getRatioDetailList();
            for (XpdGridRatioDetailVO xpdGridRatioDetailVO : ratioDetailList1) {
                Map<String, String> map = new HashMap<>();
                List<XpdCellRatioVO> cellRatioList = xpdGridRatioDetailVO.getCellRatioList();
                List<String> list = cellRatioList.stream().map(XpdCellRatioVO::getCellName).toList();
                map.put("落位宫格", String.join(",", list));
                map.put("人员占比", xpdGridRatioDetailVO.getRatio() + "%");
                mapList.add(map);
            }
            resMap.put(dimCombName, mapList);
        }

        dto.setPosRatio(JSON.toJSONString(resMap));


        return dto;
    }

    @Override
    public Pair<String, String> entityInfo(
        String param, XpdGridRatioEditLogDto beforeObj, XpdGridRatioEditLogDto afterObj,
        AuditLogBasicBean logBasic) {
        /*if (CollectionUtils.isEmpty(param)) {
            return null;
        }

        XpdTempRatioCmd xpdTempRatioCmd = param.get(0);
        List<XpdCellRatioCmd> ratioDetailList = xpdTempRatioCmd.getRatioDetailList();
        if (CollectionUtils.isEmpty(ratioDetailList)) {
            return null;
        }

        XpdCellRatioCmd xpdCellRatioCmd = ratioDetailList.get(0);
        XpdGridRatioPO xpdGridRatio = gridRatioMapper.selectByPrimaryKey(xpdCellRatioCmd.getCellRatioId());
        String gridId = xpdGridRatio.getGridId();*/
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(param);

        return Pair.of(param, "宫格模板-" + xpdGrid.getGridName() + "-落位比例");
    }
}

<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task.DmpTaskMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO">
        <!--@mbg.generated-->
        <!--@Table rv_dmp_task-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="dmp_id" jdbcType="CHAR" property="dmpId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_type" jdbcType="TINYINT" property="taskType"/>
        <result column="eval_form_id" jdbcType="CHAR" property="evalFormId"/>
        <result column="active_status" jdbcType="TINYINT" property="activeStatus"/>
        <result column="active_time" jdbcType="TIMESTAMP" property="activeTime"/>
        <result column="task_status" jdbcType="TINYINT" property="taskStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , dmp_id
             , task_name
             , task_type
             , eval_form_id
             , active_status
             , active_time
             , task_status
             , remark
             , deleted
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <select id="selectByOrgIdAndId" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task
        where id = #{id,jdbcType=CHAR}
          and org_id = #{orgId,jdbcType=CHAR}
          and deleted = 0
    </select>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO">
        <!--@mbg.generated-->
        insert into rv_dmp_task
        (id,
        org_id,
        dmp_id,
        task_name,
        task_type,
        eval_form_id,
        active_status,
        active_time,
        task_status,
        remark,
        deleted,
        create_user_id,
        create_time,
        update_user_id,
        update_time)
        values
        (#{id,jdbcType=CHAR},
        #{orgId,jdbcType=CHAR},
        #{dmpId,jdbcType=CHAR},
        #{taskName,jdbcType=VARCHAR},
        #{taskType,jdbcType=TINYINT},
        #{evalFormId,jdbcType=CHAR},
        #{activeStatus,jdbcType=TINYINT},
        #{activeTime,jdbcType=TIMESTAMP},
        #{taskStatus,jdbcType=TINYINT},
        #{remark,jdbcType=VARCHAR},
        #{deleted,jdbcType=TINYINT},
        #{createUserId,jdbcType=CHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateUserId,jdbcType=CHAR},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateById"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskPO">
        <!--@mbg.generated-->
        update rv_dmp_task
        set org_id = #{orgId,jdbcType=CHAR},
        dmp_id = #{dmpId,jdbcType=CHAR},
        task_name = #{taskName,jdbcType=VARCHAR},
        task_type = #{taskType,jdbcType=TINYINT},
        eval_form_id = #{evalFormId,jdbcType=CHAR},
        active_status = #{activeStatus,jdbcType=TINYINT},
        active_time = #{activeTime,jdbcType=TIMESTAMP},
        task_status = #{taskStatus,jdbcType=TINYINT},
        remark = #{remark,jdbcType=VARCHAR},
        deleted = #{deleted,jdbcType=TINYINT},
        create_user_id = #{createUserId,jdbcType=CHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user_id = #{updateUserId,jdbcType=CHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=CHAR}
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_dmp_task
        (id,
        org_id,
        dmp_id,
        task_name,
        task_type,
        eval_form_id,
        active_status,
        active_time,
        task_status,
        remark,
        deleted,
        create_user_id,
        create_time,
        update_user_id,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
            #{item.orgId,jdbcType=CHAR},
            #{item.dmpId,jdbcType=CHAR},
            #{item.taskName,jdbcType=VARCHAR},
            #{item.taskType,jdbcType=TINYINT},
            #{item.evalFormId,jdbcType=CHAR},
            #{item.activeStatus,jdbcType=TINYINT},
            #{item.activeTime,jdbcType=TIMESTAMP},
            #{item.taskStatus,jdbcType=TINYINT},
            #{item.remark,jdbcType=VARCHAR},
            #{item.deleted,jdbcType=TINYINT},
            #{item.createUserId,jdbcType=CHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUserId,jdbcType=CHAR},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="search" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task a
        where a.org_id = #{criteria.orgId}
        and a.deleted = 0
        <if test="criteria.activeStatus != null">
            and a.active_status = #{criteria.activeStatus}
        </if>
        <if test="criteria.taskStatus != null">
            and a.task_status = #{criteria.taskStatus}
        </if>
        <if test="criteria.taskType != null">
            and a.task_type = #{criteria.taskType}
        </if>
        <if test="criteria.taskNameLike != null and criteria.taskNameLike != ''">
            and a.task_name like concat('%', #{criteria.escapedTaskNameLike}, '%')
        </if>
        <if test="criteria.taskName != null and criteria.taskName != ''">
            and a.task_name = #{criteria.taskName}
        </if>
        <if test="criteria.dmpId != null and criteria.dmpId != ''">
            and a.dmp_id = #{criteria.dmpId}
        </if>
        <if test="criteria.evalFormId != null and criteria.evalFormId != ''">
            and a.eval_form_id = #{criteria.evalFormId}
        </if>
    </select>
    <select id="countDmpTask" resultType="java.lang.Integer">
        select count(*) as cnt
        from rv_dmp_task
        where org_id = #{orgId} and dmp_id = #{dmpId} and deleted = 0
    </select>

    <select id="countDmpTaskNumByStatus" resultType="java.lang.Integer">
        select count(*)
        from rv_dmp_task
        where org_id = #{orgId}
        and dmp_id = #{dmpId}
        <if test="types != null and types.size() > 0">
            and task_status in
            <foreach collection="types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        and deleted = 0
    </select>

    <select id="searchByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task a
        where a.org_id = #{orgId}
          and a.dmp_id = #{dmpId}
          and a.deleted = 0
        <if test="taskNameLike != null and taskNameLike != ''">
            and a.task_name like concat('%', #{taskNameLike}, '%')
        </if>
        <if test="dmpId != null and dmpId != ''">
        </if>
    </select>

    <update id="updateStatusByDmpId">
        update rv_dmp_task
        set task_status    = #{status},
            update_user_id = #{userId},
            update_time    = now()
        where org_id = #{orgId}
          and dmp_id = #{dmpId}
          and deleted = 0
    </update>
    <update id="withdrawTask">
        update rv_dmp_task
        set task_status = 5,
        update_user_id = #{userId},
        update_time = now()
        where org_id = #{orgId}
        and dmp_id = #{dmpId}
        and deleted = 0
    </update>

    <update id="updateTaskStatus">
        update rv_dmp_task
        set task_status = #{taskStatus},
            update_user_id = #{userId},
            update_time = now()
        where org_id = #{orgId}
          and id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
          and deleted = 0
    </update>





    <select id="selectByStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task a
        where a.org_id = #{orgId}
        and a.deleted = 0
        <if test="dmpId != null and dmpId != ''">
            and a.dmp_id = #{dmpId}
        </if>
        <if test="status != null and status.size() > 0">
            and a.task_status in
            <foreach collection="status" item="statu" open="(" separator="," close=")">
                #{statu}
            </foreach>
        </if>
    </select>

    <select id="countDmpTaskByActiveStatusIn" resultType="java.lang.Integer">
        select count(*)
        from rv_dmp_task
        where org_id = #{orgId}
          and dmp_id = #{dmpId}
        <if test="activeStatusList != null and activeStatusList.size() > 0">
            and active_status in
            <foreach collection="activeStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and deleted = 0
    </select>

    <select id="countTaskNumGroup"
            resultType="com.yxt.talent.rv.application.dmp.task.dto.DmpTaskNumDTO">
        select dmp_id as dmpId, count(*) as taskNum
        from rv_dmp_task a
        where org_id = #{orgId}
          and dmp_id in
        <foreach collection="dmpIds" item="dmpId" open="(" separator="," close=")">
            #{dmpId}
        </foreach>
        and deleted = 0
        and a.task_type = 0
        and active_status != 0
        group by dmp_id
    </select>
    <select id="countFormTask" resultType="java.lang.Long">
        select count(*)
        from rv_dmp_task
        where org_id = #{orgId}
          and dmp_id = #{dmpId}
          and deleted = 0
          and task_type = 1
    </select>

    <insert id="batchInsertOrUpdate">
        insert into rv_dmp_task
            (id,
             org_id,
             dmp_id,
             task_name,
             task_type,
             eval_form_id,
             active_status,
             active_time,
             task_status,
             remark,
             deleted,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.dmpId,jdbcType=CHAR},
             #{item.taskName,jdbcType=VARCHAR},
             #{item.taskType,jdbcType=TINYINT},
             #{item.evalFormId,jdbcType=CHAR},
             #{item.activeStatus,jdbcType=TINYINT},
             #{item.activeTime,jdbcType=TIMESTAMP},
             #{item.taskStatus,jdbcType=TINYINT},
             #{item.remark,jdbcType=VARCHAR},
             #{item.deleted,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            dmp_id         = values(dmp_id),
            task_name      = values(task_name),
            task_type      = values(task_type),
            eval_form_id   = values(eval_form_id),
            active_status  = values(active_status),
            active_time    = values(active_time),
            task_status    = values(task_status),
            remark         = values(remark),
            deleted        = values(deleted),
            update_user_id = values(update_user_id),
            update_time    = values(update_time)
        </trim>
    </insert>


    <select id="selectByFormId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task a
        where a.org_id = #{orgId}
        and a.deleted = 0
        <if test="dmpId != null and dmpId != ''">
            and a.dmp_id = #{dmpId}
        </if>
        <if test="formIds != null and formIds.size() > 0">
            and a.eval_form_id in
            <foreach collection="formIds" item="formId" open="(" separator="," close=")">
                #{formId}
            </foreach>
        </if>
    </select>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_task a
        where a.org_id = #{sourceOrgId}
        and a.deleted = 0
        and exists(select 1 from rv_dmp b where b.org_id = #{sourceOrgId} and b.deleted = 0 and a.dmp_id = b.id)
    </select>
</mapper>

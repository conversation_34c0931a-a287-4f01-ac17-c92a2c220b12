<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserDimResultMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO">
        <!--@mbg.generated-->
        <!--@Table rv_dmp_user_dim_result-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="dmp_id" property="dmpId"/>
        <result column="task_id" property="taskId"/>
        <result column="user_id" property="userId"/>
        <result column="dim_id" property="dimId"/>
        <result column="jq_dim_id" property="jqDimId"/>
        <result column="matched" property="matched"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="form_score" property="formScore"/>
        <result column="skill_level_id" property="skillLevelId"/>
        <result column="exception" property="exception"/>
        <result column="score" property="score"/>
        <result column="weight_score" property="weightScore"/>
        <result column="form_clac_time" property="formClacTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , dmp_id
             , task_id
             , user_id
             , dim_id
             , jq_dim_id
             , matched
             , create_user_id
             , create_time
             , update_user_id
             , update_time
             , form_score
             , skill_level_id
             , exception
             , score
             , weight_score
             , form_clac_time
    </sql>
    <insert id="insertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO">
        <!--@mbg.generated-->
        insert into rv_dmp_user_dim_result
            (id,
             org_id,
             dmp_id,
             task_id,
             user_id,
             dim_id,
             jq_dim_id,
             matched,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             form_score,
             skill_level_id,
             exception,
             score,
             weight_score,
             form_clac_time)
        values
            (#{id},
             #{orgId},
             #{dmpId},
             #{taskId},
             #{userId},
             #{dimId},
             #{jqDimId},
             #{matched},
             #{createUserId},
             #{createTime},
             #{updateUserId},
             #{updateTime},
             #{formScore},
             #{skillLevelId},
             #{exception},
             #{score},
             #{weightScore},
             #{formClacTime})
        on duplicate key update
        <trim suffixOverrides=",">
            dmp_id         = #{dmpId},
            task_id        = #{taskId},
            user_id        = #{userId},
            dim_id         = #{dimId},
            jq_dim_id      = #{jqDimId},
            `matched`      = #{matched},
            update_user_id = #{updateUserId},
            update_time    = #{updateTime},
            form_score     = #{formScore},
            skill_level_id = #{skillLevelId},
            `exception`    = #{exception},
            score          = #{score},
            weight_score   = #{weightScore},
            form_clac_time = #{formClacTime}
        </trim>
    </insert>

    <select id="selectByOrgIdAndId" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_dim_result
        where id = #{id,jdbcType=CHAR}
        and org_id = #{orgId,jdbcType=CHAR}
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_dmp_user_dim_result
            (id,
             org_id,
             dmp_id,
             task_id,
             user_id,
             dim_id,
             jq_dim_id,
             form_score,
             score,
             weight_score,
             skill_level_id,
             matched,
             form_clac_time,
             create_user_id,
             create_time,
             update_user_id,
             update_time,
             exception)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.dmpId,jdbcType=CHAR},
             #{item.taskId,jdbcType=CHAR},
             #{item.userId,jdbcType=CHAR},
             #{item.dimId,jdbcType=CHAR},
             #{item.jqDimId,jdbcType=CHAR},
             #{item.formScore,jdbcType=DECIMAL},
             #{item.score,jdbcType=DECIMAL},
             #{item.weightScore,jdbcType=DECIMAL},
             #{item.skillLevelId,jdbcType=CHAR},
             #{item.matched,jdbcType=TINYINT},
             #{item.formClacTime,jdbcType=TIMESTAMP},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.exception,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO">
        <!--@mbg.generated-->
        insert into rv_dmp_user_dim_result
            (id,
             org_id,
             dmp_id,
             task_id,
             user_id,
             dim_id,
             jq_dim_id,
             form_score,
             score,
             weight_score,
             skill_level_id,
             matched,
             exception,
             form_clac_time,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.dmpId,jdbcType=CHAR},
             #{item.taskId,jdbcType=CHAR},
             #{item.userId,jdbcType=CHAR},
             #{item.dimId,jdbcType=CHAR},
             #{item.jqDimId,jdbcType=CHAR},
             #{item.formScore,jdbcType=DECIMAL},
             #{item.score,jdbcType=DECIMAL},
             #{item.weightScore,jdbcType=DECIMAL},
             #{item.skillLevelId,jdbcType=CHAR},
             #{item.matched,jdbcType=TINYINT},
             #{item.exception,jdbcType=TINYINT},
             #{item.formClacTime,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id         = values(org_id),
            dmp_id         = values(dmp_id),
            task_id        = values(task_id),
            user_id        = values(user_id),
            dim_id         = values(dim_id),
            jq_dim_id      = values(jq_dim_id),
            form_score     = values(form_score),
            score          = values(score),
            weight_score   = values(weight_score),
            skill_level_id = values(skill_level_id),
            matched        = values(matched),
            exception      = values(exception),
            form_clac_time = values(form_clac_time),
            update_user_id = values(update_user_id),
            update_time    = values(update_time)
        </trim>
    </insert>

    <select id="search" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_dim_result a
        where a.org_id = #{criteria.orgId}
        <if test="criteria.dmpId != null and criteria.dmpId != ''">
            and a.dmp_id = #{criteria.dmpId}
        </if>
        <if test="criteria.taskId != null and criteria.taskId != ''">
            and a.task_id = #{criteria.taskId}
        </if>
        <if test="criteria.userId != null and criteria.userId != ''">
            and a.user_id = #{criteria.userId}
        </if>
        <if test="criteria.jqDimId != null and criteria.jqDimId != ''">
            and a.jq_dim_id = #{criteria.jqDimId}
        </if>
        <if test="criteria.matched != null">
            and a.matched = #{criteria.matched}
        </if>
        <if test="criteria.userIds != null and criteria.userIds.size() != 0">
            and a.user_id in
            <foreach close=")" collection="criteria.userIds" index="index" item="item" open="("
                     separator=",">
            #{item}
            </foreach>
        </if>
    </select>

    <select id="selectPage"
            resultType="com.yxt.talent.rv.controller.openapi.viewobj.DmpUserDimResultOpenVO">
        with user_latest_dim_reuslt as (
            select t.org_id
                 , t.user_id
                 , t.thirduserid
                 , t.thirddeptid
                 , t.thirddeptname
                 , t.dmpid
                 , t.cataid
                 , t.cataname
                 , t.dimid
                 , t.dimname
                 , t.achieved
                 , t.form_clac_time
            from (
                select a.org_id
                     , a.user_id
                     , d.id                                                                                as dmpid
                     , b.third_user_id                                                                     as thirduserid
                     , f.third_id                                                                          as thirddeptid
                     , f.name                                                                              as thirddeptname
                     , coalesce(c.jq_tpl_cat_id, c.jq_cata_id)                                             as cataid
                     , coalesce(c.jq_tpl_cat_name, c.jq_cata_name)                                         as cataname
                     , a.jq_dim_id                                                                         as dimid
                     , c.jq_dim_name                                                                       as dimname
                     , a.matched                                                                           as achieved
                     , d.create_time
                     , a.form_clac_time
                     , row_number() over (partition by a.user_id, a.jq_dim_id order by a.update_time desc) as ranks
                from rv_dmp_user_dim_result a
                join udp_lite_user_sp       b
                     on a.user_id = b.id and b.deleted = 0 and b.third_user_id != '' and
                        b.org_id = #{orgId}
                join udp_dept               f on f.id = b.dept_id
                join rv_dmp_task_dim        c on c.id = a.dim_id and c.deleted = 0
                join rv_dmp                 d
                     on d.id = a.dmp_id and d.deleted = 0 and d.dmp_status = 4
                join rv_dmp_pos             e
                     on d.id = e.dmp_id and e.deleted = 0 and b.position_id = e.position_id
                where a.org_id = #{orgId}
            ) as t
            where t.ranks = 1
        )
           ,
        user_latest_dmp             as (
        select t.user_id, t.dmpid
        from (
            select a.org_id
                 , a.user_id
                 , d.id                                                                    as dmpid
                 , row_number() over ( partition by a.user_id order by d.create_time desc) as ranks
            from rv_dmp_user_result a
            join udp_lite_user_sp   b
                 on a.user_id = b.id and b.deleted = 0 and b.third_user_id != '' and b.org_id = #{orgId}
            join rv_dmp             d on d.id = a.dmp_id and d.deleted = 0 and d.dmp_status = 4
            join rv_dmp_pos         e
                 on d.id = e.dmp_id and e.deleted = 0 and b.position_id = e.position_id
            where a.org_id = #{orgId}
            <if test="searchStartTime != null and searchStartTime != ''">
                and d.create_time &gt;= #{searchStartTime}
            </if>
            <if test="searchEndTime != null and searchEndTime != ''">
                and d.create_time &lt; #{searchEndTime}
            </if>
            ) as t
            where t.ranks = 1
        )

        select t.*
        from user_latest_dim_reuslt t
        join user_latest_dmp        p on t.user_id = p.user_id and t.dmpId = p.dmpId
        order by t.user_id, t.dimId
    </select>


    <select id="selectByDmpIdAndUserIds"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO">
        select a.id
             , a.org_id
             , a.dmp_id
             , a.task_id
             , a.user_id
             , b.dim_type          as dimtype
             , a.dim_id
             , a.jq_dim_id
             , b.jq_skill_model_id as jqskillmodelid
             , b.jq_rt_model_id    as jqrtmodelid
             , a.matched
             , a.form_score
             , a.score
             , a.weight_score
             , a.skill_level_id
             , a.form_clac_time
        from rv_dmp_user_dim_result a
        join rv_dmp_task_dim        b on a.dim_id = b.id and b.deleted = 0
        where a.org_id = #{orgId}
          and a.dmp_id = #{dmpId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and a.user_id in
                <foreach close=")" collection="userIds" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="listByTaskIdAndUserIdAndDimIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_dim_result a
        where a.org_id = #{orgId}
          and a.user_id = #{userId}
          and a.task_id = #{taskId}
        <choose>
            <when test="dimIds != null and dimIds.size() != 0">
                and a.dim_id in
                <foreach close=")" collection="dimIds" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <delete id="deleteBatch">
        delete from rv_dmp_user_dim_result
        where org_id = #{orgId}
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach close=")" collection="ids" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectFacadeList" resultType="com.yxt.talentrvfacade.bean.DmpUserDimResultFacadeVO">
        select a.org_id
             , a.dmp_id
             , a.user_id
             , b.dim_type
             , a.jq_dim_id
             , a.matched
             , a.form_clac_time as calcTime
             , (select t.create_time from rv_dmp_task t where t.id = a.task_id) as taskTime
        from rv_dmp_user_dim_result a
        join rv_dmp_task_dim        b on a.dim_id = b.id and a.dmp_id = b.dmp_id and b.deleted = 0
        where a.org_id = #{orgId}
          and b.dim_type in (4, 5)
          and exists
        (
            select 1
            from rv_dmp      o
            join rv_dmp_task p on o.id = p.dmp_id
            where o.deleted = 0
              and p.deleted = 0
              and a.dmp_id = o.id
              and a.task_id = p.id
        )
        <if test="query.dmpId != null and query.dmpId != ''">
            and a.dmp_id = #{query.dmpId}
        </if>
        <choose>
            <when test="query.userIds != null and query.userIds.size() != 0">
                and a.user_id in
                <foreach close=")" collection="query.userIds" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <delete id="deleteByDimIds">
        delete from rv_dmp_user_dim_result
        where org_id = #{orgId} and dmp_id = #{dmpId}
        <choose>
            <when test="dimIds != null and dimIds.size() != 0">
                and dim_id in
                <foreach close=")" collection="dimIds" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteByUserIds">
        delete from rv_dmp_user_dim_result
        where org_id = #{orgId} and dmp_id = #{dmpId}
        <choose>
            <when test="delUserIds != null and delUserIds.size() != 0">
                and user_id in
                <foreach close=")" collection="delUserIds" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteByDmpId">
        delete from rv_dmp_user_dim_result where org_id = #{orgId} and dmp_id = #{dmpId}
    </delete>

    <select id="findDmpUserRtInfos"
            resultType="com.yxt.talent.rv.controller.facade.viewobj.DmpUserRtInfoVO">
        select a.dmp_id         as dmpId
             , a.user_id        as userId
             , b.jq_rt_model_id as rtModelId
             , b.jq_dim_id      as rtId
             , b.jq_dim_name    as rtName
             , a.matched
        from rv_dmp_user_dim_result a
        left join rv_dmp_task_dim   b
                  on a.org_id = b.org_id and a.dmp_id = b.dmp_id and a.dim_id = a.id and
                     b.dim_type = 5 and b.deleted = 0
        where a.org_id = #{orgId}
          and a.dmp_id = #{dmpId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and a.user_id in
                <foreach close=")" collection="userIds" index="index" item="item" open="("
                         separator=",">
                #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        order by a.user_id, b.order_index
    </select>

    <select id="findUserResultInfos"
            resultType="com.yxt.talent.rv.controller.manage.dmp.viewobj.userview.DmpUserDimResultVO">
        select a.matched        as matched
             , a.skill_level_id as skillLevelId
             , a.form_score     as formScore
             , a.score
             , a.weight_score   as weightScore
             , a.jq_dim_id      as jqDimId
             , b.jq_dim_name    as jqDimName
             , b.jq_cata_id     as jqCataId
             , b.jq_cata_name   as jqCataName
             , c.task_type      as taskType
             , b.dim_type       as dimType
        from rv_dmp_user_dim_result a
        left join rv_dmp_task_dim   b on a.org_id = b.org_id and a.dim_id = b.id
        left join rv_dmp_task       c on b.task_id = c.id and b.org_id = c.org_id
        where a.org_id = #{orgId}
          and a.dmp_id = #{dmpId}
          and a.user_id = #{userId}
        order by b.order_index
    </select>

    <select id="findResultNum"
            resultType="com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpTaskResultNumDTO">
        select task_id as taskId, count(*) as resultNum
        from rv_dmp_user_dim_result
        where org_id = #{orgId}
          and task_id in
        <foreach close=")" collection="taskIds" index="index" item="taskId" open="(" separator=",">
            #{taskId}
        </foreach>
        group by task_id
    </select>

    <delete id="deleteByDmpIdAndTaskIdAndDimId">
        delete
        from rv_dmp_user_dim_result
        where org_id = #{orgId}
          and dmp_id = #{dmpId}
          and task_id = #{taskId}
        <if test="dimId != null and dimId != ''">
            and dim_id = #{dimId}
        </if>
    </delete>

    <select id="selectByDmpIdAndTaskId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserDimResultPO">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_dim_result
        where org_id = #{orgId}
          and dmp_id = #{dmpId}
          and task_id = #{taskId}
        <if test="dimId != null and dimId != ''">
            and dim_id = #{dimId}
        </if>
    </select>

    <update id="batchUpdateScoreAndWeightScore">
        update rv_dmp_user_dim_result
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id}
                        then #{item.score}
                </foreach>
            </trim>
            <trim prefix="weight_score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id}
                        then #{item.weightScore}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_dim_result a
        where a.org_id = #{orgId}
        <!--只查询dmp和dmoTask未删除的记录-->
        and exists
        (
            select 1
            from rv_dmp      o
            join rv_dmp_task p on o.id = p.dmp_id
            where o.deleted = 0 and p.deleted = 0
            and o.id = a.dmp_id and p.id = a.task_id
        )
    </select>
</mapper>